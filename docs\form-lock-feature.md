# Form Lock Feature Documentation

## Overview

The Form Lock feature prevents multiple users from editing the same application form simultaneously, ensuring data integrity while allowing read-only access for other users. This feature addresses the critical business requirement of avoiding conflicting changes when multiple applicants work on the same project's application form.

## Business Requirements

### User Stories

**As an applicant, I want to be able to edit a project's application form only when no other applicant is editing it, so that we avoid conflicting changes and ensure data integrity, while others can still view the form in read-only mode or access previous versions.**

### Acceptance Criteria

✅ **Exclusive Editing**: Only one applicant can edit the form at a time  
✅ **Read-Only Access**: Other applicants can view the form in read-only mode  
✅ **Lock Release**: Edit lock is released after 10 minutes of inactivity  
✅ **Manual Release**: Edit lock is released when user stops editing  
✅ **User Notification**: Clear notifications when form is locked with editor information

## Technical Architecture

### System Components

```mermaid
graph TB
    A[Project List] --> B[Form Lock Check]
    B --> C{Form Locked?}
    C -->|No| D[Navigate to Edit Mode]
    C -->|Yes| E[Show Confirmation Dialog]
    E -->|Proceed| F[Navigate to Read-Only Mode]
    E -->|Cancel| G[Stay on Project List]

    D --> H[Form Submission Page]
    F --> H

    H --> I[Form Lock Context]
    I --> J[Lock Status Check]
    J --> K[Form Lock Service]
    K --> L[Backend API]

    I --> M[Auto-Refresh Timer]
    I --> N[Inactivity Timer]
    I --> O[Activity Tracking]
```

### Core Components

1. **FormLockService** - API communication layer
2. **FormLockContext** - Global state management
3. **useFormLock Hook** - Component integration interface
4. **FormLockStatus** - UI status display
5. **ReadOnlyFormWrapper** - Input disabling wrapper

## API Specification

### Endpoints

| Method | Endpoint            | Description         | Request Body                           | Response                                     |
| ------ | ------------------- | ------------------- | -------------------------------------- | -------------------------------------------- |
| POST   | `/api/v1/form-lock` | Acquire form lock   | `{formId, projectRef}`                 | `{success, lockId, lockedBy*, expiresAt}`    |
| DELETE | `/api/v1/form-lock` | Release form lock   | Query params                           | `{success, message}`                         |
| GET    | `/api/v1/form-lock` | Get lock status     | Query params                           | `{isLocked, lockedBy*, lockedAt, expiresAt}` |
| PUT    | `/api/v1/form-lock` | Refresh lock expiry | `{formId, projectRef, lockedByUserId}` | `{success, expiresAt}`                       |

\*lockedBy fields include: `lockedByUserId`, `lockedByUserName`, `lockedByUserEmail`

### Request/Response Examples

**Acquire Lock Request:**

```json
POST /api/v1/form-lock
{
  "formId": "form-123",
  "projectRef": "project-456"
}
```

**Acquire Lock Response (Success):**

```json
{
  "success": true,
  "lockId": "lock-789",
  "lockedByUserId": "user-123",
  "lockedByUserName": "John Doe",
  "lockedByUserEmail": "<EMAIL>",
  "lockedAt": "2024-01-15T10:30:00Z",
  "expiresAt": "2024-01-15T10:40:00Z"
}
```

**Acquire Lock Response (Conflict):**

```json
{
  "success": false,
  "message": "Form is already locked by another user",
  "lockedByUserId": "user-456",
  "lockedByUserName": "Jane Smith",
  "lockedByUserEmail": "<EMAIL>"
}
```

## User Experience Flow

### 1. Project List Interaction

When a user clicks "Edit Application" from the project list:

```typescript
// In entity-config-registry.ts
handler: async (row) => {
  try {
    // Get active form
    const formResponse = await apiClient.get(
      `/forms/actives?formType=APPLICATION&projectRef=${row.projectRef}`
    );
    const formId = formResponse.data.id;

    // Check lock status
    const lockStatus = await apiClient.get(
      `/form-lock?formId=${formId}&projectRef=${row.projectRef}`
    );

    if (lockStatus.data.isLocked && lockStatus.data.lockedByUserId) {
      const lockedByName = lockStatus.data.lockedByUserName || "another user";

      // Show confirmation dialog
      const proceed = window.confirm(
        `This form is currently being edited by ${lockedByName}. ` +
          `You can view it in read-only mode or wait for them to finish. ` +
          `Do you want to proceed to view it in read-only mode?`
      );

      if (!proceed) return;
    }

    // Navigate to form
    window.location.href = `/applications/${formId}/submit/${row.projectRef}`;
  } catch (error) {
    alert("Unable to access the application form. Please try again later.");
  }
};
```

### 2. Form Submission Page Experience

#### Initial Load

1. Form loads with lock status check
2. FormLockStatus component displays current state
3. Form is wrapped in ReadOnlyFormWrapper

#### Lock States and UI

| Lock State                 | UI Behavior                                                                | User Actions Available                          |
| -------------------------- | -------------------------------------------------------------------------- | ----------------------------------------------- |
| **Unlocked**               | ✅ Form editable<br/>🟢 "Available for editing" badge                      | • Start Editing button                          |
| **Locked by Current User** | ✅ Form editable<br/>🔵 "You are editing" badge<br/>⏱️ Timer display       | • Stop Editing button<br/>• Auto-refresh active |
| **Locked by Other User**   | ❌ Form read-only<br/>🔴 "Locked by [Name]" badge<br/>👤 User info display | • View Read-Only button<br/>• Try Again button  |
| **Error**                  | ⚠️ Error message<br/>🟡 Fallback to unlocked                               | • Retry button                                  |

#### Visual Indicators

**Lock Status Badge:**

```tsx
<Badge variant="outline" className="bg-green-100 text-green-800">
  <Lock className="h-4 w-4" />
  <span className="ml-1">You are editing</span>
</Badge>
```

**Read-Only Overlay:**

```tsx
<div className="absolute inset-0 z-10 bg-gray-50/50 backdrop-blur-[0.5px] pointer-events-none" />
```

## Timer Management

### Configuration

```typescript
const DEFAULT_TIMER_CONFIG = {
  refreshInterval: 5 * 60 * 1000, // 5 minutes - Auto-refresh frequency
  inactivityTimeout: 10 * 60 * 1000, // 10 minutes - Inactivity timeout
  warningTime: 2 * 60 * 1000, // 2 minutes - Warning before timeout
  retryInterval: 30 * 1000, // 30 seconds - Retry delay
};
```

### Timer Lifecycle

1. **Lock Acquisition**: Start refresh and inactivity timers
2. **Activity Detection**: Reset inactivity timer on user interaction
3. **Auto-Refresh**: Refresh lock every 5 minutes while active
4. **Inactivity Warning**: Show warning 2 minutes before timeout
5. **Auto-Release**: Release lock after 10 minutes of inactivity

### Activity Tracking

The system tracks user activity through multiple event listeners:

```typescript
const events = [
  "mousedown",
  "mousemove",
  "keypress",
  "scroll",
  "touchstart",
  "click",
];
```

## Implementation Guide

### 1. Backend Requirements

Implement the 4 API endpoints with the following business logic:

**Lock Acquisition:**

- Check if form is already locked
- If locked by different user, return conflict
- If unlocked or expired, create new lock
- Set expiration time (10 minutes from now)

**Lock Status:**

- Return current lock information
- Include user details for locked forms
- Handle expired locks appropriately

**Lock Refresh:**

- Verify lock ownership
- Extend expiration time
- Return new expiration timestamp

**Lock Release:**

- Verify lock ownership or admin privileges
- Remove lock from storage
- Return success confirmation

### 2. Frontend Integration

**Step 1: Wrap Application with Provider**

```tsx
// In App.tsx or main layout
import { FormLockProvider } from "@/contexts/FormLockContext";

function App() {
  return (
    <FormLockProvider>
      <YourAppContent />
    </FormLockProvider>
  );
}
```

**Step 2: Integrate with Form Pages**

```tsx
// In FormSubmissionPage.tsx
import { useFormLock } from "@/hooks/useFormLock";
import { FormLockStatus } from "@/components/form-lock/FormLockStatus";
import { ReadOnlyFormWrapper } from "@/components/form-lock/ReadOnlyFormWrapper";

function FormSubmissionPage() {
  const { isReadOnly } = useFormLock({
    formId: "your-form-id",
    projectRef: "your-project-ref",
    autoAcquire: false,
    autoRelease: true,
  });

  return (
    <div>
      <FormLockStatus showDetails={true} showActions={true} />

      <ReadOnlyFormWrapper>
        <YourFormContent />
      </ReadOnlyFormWrapper>
    </div>
  );
}
```

### 3. Customization Options

**Timer Configuration:**

```typescript
// Custom timer settings
const customConfig = {
  refreshInterval: 3 * 60 * 1000, // 3 minutes
  inactivityTimeout: 15 * 60 * 1000, // 15 minutes
  warningTime: 3 * 60 * 1000, // 3 minutes
};
```

**Event Callbacks:**

```typescript
const formLock = useFormLock({
  formId: "form-123",
  projectRef: "project-456",
  onLockAcquired: () => console.log("Lock acquired"),
  onLockFailed: (error) => console.error("Lock failed:", error),
  onReadOnlyMode: () => console.log("Switched to read-only"),
  onInactivityWarning: (time) => console.log(`Warning: ${time}ms remaining`),
});
```

## Security Considerations

### Authentication & Authorization

- **User Verification**: All lock operations verify user authentication
- **Lock Ownership**: Only lock owner can refresh or release their lock
- **Admin Override**: System administrators can release any lock
- **Session Management**: Locks are tied to user sessions

### Data Protection

- **Input Sanitization**: All API inputs are validated and sanitized
- **SQL Injection Prevention**: Use parameterized queries for database operations
- **XSS Protection**: User names and emails are properly escaped in UI
- **CSRF Protection**: API endpoints include CSRF tokens

### Rate Limiting

- **Lock Acquisition**: Limit attempts per user per minute
- **Status Checks**: Reasonable limits on status polling
- **Refresh Operations**: Prevent excessive refresh requests

## Error Handling & Edge Cases

### Network Failures

```typescript
// Retry logic with exponential backoff
const acquireLockWithRetry = async (
  request,
  maxRetries = 3,
  retryDelay = 1000
) => {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await FormLockService.acquireLock(request);
    } catch (error) {
      if (attempt < maxRetries) {
        await new Promise((resolve) =>
          setTimeout(resolve, retryDelay * (attempt + 1))
        );
      } else {
        throw error;
      }
    }
  }
};
```

### Concurrent Access Scenarios

| Scenario                                  | Behavior                                  | User Experience                                             |
| ----------------------------------------- | ----------------------------------------- | ----------------------------------------------------------- |
| **Two users click "Edit" simultaneously** | First request wins, second gets conflict  | Second user sees "locked by [Name]" message                 |
| **Lock expires while user is editing**    | Auto-refresh attempts, then shows warning | User gets 2-minute warning to extend session                |
| **Network disconnection during editing**  | Lock maintained until expiry              | User can continue editing, auto-refresh resumes when online |
| **Browser crash/close**                   | Lock remains until expiry                 | Other users must wait for timeout or admin intervention     |

### Fallback Strategies

1. **API Unavailable**: Default to unlocked state (fail-open approach)
2. **Invalid Responses**: Log errors, show user-friendly messages
3. **Timer Failures**: Graceful degradation without breaking form functionality
4. **Authentication Issues**: Redirect to login, preserve form data

## Monitoring & Analytics

### Key Metrics to Track

- **Lock Acquisition Rate**: Success/failure ratio
- **Average Lock Duration**: How long users typically hold locks
- **Conflict Frequency**: How often users encounter locked forms
- **Timeout Rate**: Percentage of locks released due to inactivity
- **Error Rates**: API failures and their causes

### Logging Requirements

```typescript
// Example log entries
{
  "event": "lock_acquired",
  "userId": "user-123",
  "formId": "form-456",
  "projectRef": "project-789",
  "timestamp": "2024-01-15T10:30:00Z"
}

{
  "event": "lock_conflict",
  "requestingUserId": "user-123",
  "lockOwnerUserId": "user-456",
  "formId": "form-456",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

## Testing Strategy

### Unit Tests

- **Service Layer**: API communication and error handling
- **Context Provider**: State management and timer logic
- **Hooks**: Component integration and lifecycle
- **Components**: UI rendering and user interactions

### Integration Tests

- **End-to-End Scenarios**: Complete user workflows
- **Concurrent Access**: Multiple users accessing same form
- **Timer Behavior**: Inactivity and refresh cycles
- **Error Recovery**: Network failures and reconnection

### Performance Tests

- **Load Testing**: Multiple concurrent lock operations
- **Memory Leaks**: Timer cleanup and component unmounting
- **API Response Times**: Lock operations under load

## Deployment Checklist

### Backend Requirements

- [ ] Implement 4 API endpoints with proper error handling
- [ ] Set up database tables for lock storage
- [ ] Configure session management and authentication
- [ ] Implement rate limiting and security measures
- [ ] Set up monitoring and logging

### Frontend Deployment

- [ ] Verify FormLockProvider is wrapped at app level
- [ ] Test all form pages have proper integration
- [ ] Validate timer configurations match requirements
- [ ] Ensure error handling provides good UX
- [ ] Test cross-browser compatibility

### Production Validation

- [ ] Test with multiple concurrent users
- [ ] Verify lock cleanup on browser close
- [ ] Validate inactivity timeout behavior
- [ ] Test network failure scenarios
- [ ] Monitor performance and error rates

## Troubleshooting Guide

### Common Issues

**Issue**: Locks not releasing when browser closes
**Solution**: Verify beforeunload event handlers and consider server-side cleanup job

**Issue**: Users getting stuck in read-only mode
**Solution**: Check lock expiration logic and provide manual override for admins

**Issue**: Timer conflicts causing memory leaks
**Solution**: Ensure proper cleanup in useEffect dependencies

**Issue**: API rate limiting blocking legitimate requests
**Solution**: Adjust rate limits and implement proper retry logic

### Debug Tools

Enable debug mode:

```javascript
localStorage.setItem("form-lock-debug", "true");
```

This provides detailed console logging for all lock operations and timer events.

## Future Enhancements

### Potential Improvements

1. **Real-time Updates**: WebSocket integration for instant lock status updates
2. **Lock Queue**: Allow users to queue for editing when form becomes available
3. **Collaborative Editing**: Section-level locking for large forms
4. **Mobile Optimization**: Touch-friendly interfaces and mobile-specific timers
5. **Analytics Dashboard**: Admin interface for lock monitoring and management

### Scalability Considerations

- **Redis Integration**: Use Redis for distributed lock storage
- **Microservices**: Separate lock service for better scalability
- **CDN Integration**: Cache lock status for improved performance
- **Database Optimization**: Efficient indexing and cleanup strategies
