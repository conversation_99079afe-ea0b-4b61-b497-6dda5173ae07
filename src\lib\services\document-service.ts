import { apiClient } from "../api/api-client";

/**
 * Document type definition
 */
export interface Document {
  id: string;
  fileName: string;
  parentObjectId: string;
  parentObjectType: string;
  fileType: string;
  isActive: boolean;
  projectRef: string;
  tag: string;
  createdBy: string;
  createdAt: string;
}

export const DocumentService = {
  downloadMultipleDocuments: async (
    documentIds: string[],
    zipFileName?: string
  ): Promise<void> => {
    try {
      const API_BASE_URL =
        window?.ENV?.VITE_API_BASE_URL ??
        import.meta.env.VITE_API_BASE_URL ??
        "http://**********:8080/api/v1";

      const token = localStorage.getItem("cognito_id_token");

      const response = await fetch(`${API_BASE_URL}/documents/content`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ ids: documentIds }),
      });

      if (!response.ok) {
        throw new Error(`Failed to download documents: ${response.statusText}`);
      }

      const contentDisposition = response.headers.get("content-disposition");

      // Decide filename
      let filename = "document";
      if (documentIds.length === 1) {
        filename = `document-${documentIds[0]}`;
      } else {
        filename = zipFileName || `documents-${Date.now()}.zip`;
      }

      if (contentDisposition) {
        const filenameMatch = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(
          contentDisposition
        );
        const newFilename = filenameMatch?.[1];
        if (newFilename) {
          filename = newFilename.replace(/['"]/g, "");
        }
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download error:", error);
    }
  },

  /**
   * Get document metadata by ID
   */
  getDocumentById: async (documentId: string): Promise<Document | null> => {
    try {
      const response = await apiClient.get<Document>(
        `/documents/${documentId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching document:", error);
      return null;
    }
  },
};
