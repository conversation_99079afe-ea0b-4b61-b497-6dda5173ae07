import React, { createContext, useContext, useReducer, useC<PERSON>back, useRef, useEffect, useMemo } from "react";
import { useAuth } from "./AuthContext";
import { FormLockService } from "@/lib/services/form-lock-service";
import { useToast } from "@/components/ui/use-toast";
import {
  FormLockContextState,
  FormLockContextValue,
  FormLockAction,
  FormLockState,
  FormLockMode,
  DEFAULT_FORM_LOCK_TIMER_CONFIG,
} from "@/lib/types/form-lock";
import { ErrorResponse } from "@/lib/types/api";

// Initial state
const initialState: FormLockContextState = {
  lockState: FormLockState.UNLOCKED,
  formMode: FormLockMode.LOADING,
  isLoading: false,
  timerConfig: DEFAULT_FORM_LOCK_TIMER_CONFIG,
};

// Reducer function
function formLockReducer(state: FormLockContextState, action: FormLockAction): FormLockContextState {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };

    case "SET_LOCK_STATE":
      return { ...state, lockState: action.payload };

    case "SET_FORM_MODE":
      return { ...state, formMode: action.payload };

    case "SET_FORM_INFO":
      return {
        ...state,
        formId: action.payload.formId,
        projectRef: action.payload.projectRef
      };

    case "SET_LOCK_INFO":
      return {
        ...state,
        lockedByUser: action.payload.lockedByUser,
        lockedAt: action.payload.lockedAt,
        expiresAt: action.payload.expiresAt,
        lastRefreshedAt: action.payload.lastRefreshedAt,
      };

    case "CLEAR_LOCK_INFO":
      return {
        ...state,
        lockedByUser: undefined,
        lockedAt: undefined,
        expiresAt: undefined,
        lastRefreshedAt: undefined,
      };

    case "SET_ERROR":
      return { ...state, error: action.payload };

    case "CLEAR_ERROR":
      return { ...state, error: undefined };

    case "UPDATE_EXPIRY":
      return { ...state, expiresAt: action.payload };

    case "UPDATE_LAST_REFRESHED":
      return { ...state, lastRefreshedAt: action.payload };

    case "RESET":
      return { ...initialState };

    default:
      return state;
  }
}

// Create context
const FormLockContext = createContext<FormLockContextValue | null>(null);

// Provider component
export const FormLockProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(formLockReducer, initialState);
  const { user } = useAuth();
  const { toast } = useToast();

  // Timer refs
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (refreshTimerRef.current) clearInterval(refreshTimerRef.current);
      if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
      if (warningTimerRef.current) clearTimeout(warningTimerRef.current);
    };
  }, []);

  // Acquire lock
  const acquireLock = useCallback(async (formId: string, projectRef: string): Promise<boolean> => {
    if (!user) {
      dispatch({ type: "SET_ERROR", payload: "User not authenticated" });
      return false;
    }

    dispatch({ type: "SET_LOADING", payload: true });
    dispatch({ type: "CLEAR_ERROR" });
    dispatch({ type: "SET_FORM_INFO", payload: { formId, projectRef } });

    try {
      const response = await FormLockService.acquireLock({ formId, projectRef });

      if (response.success) {
        dispatch({ type: "SET_LOCK_STATE", payload: FormLockState.LOCKED_BY_CURRENT_USER });
        dispatch({ type: "SET_FORM_MODE", payload: FormLockMode.EDIT });

        if (response.lockedByUserId && response.lockedByUserName && response.lockedByUserEmail) {
          dispatch({
            type: "SET_LOCK_INFO",
            payload: {
              lockedByUser: {
                id: response.lockedByUserId,
                name: response.lockedByUserName,
                email: response.lockedByUserEmail,
              },
              lockedAt: response.lockedAt ? new Date(response.lockedAt) : new Date(),
              expiresAt: response.expiresAt ? new Date(response.expiresAt) : new Date(Date.now() + state.timerConfig.inactivityTimeout),
            },
          });
        }

        toast({
          title: "Form Lock Acquired",
          description: "You can now edit this form. Others will see it as read-only.",
          variant: "default",
        });

        return true;
      } else {
        dispatch({ type: "SET_ERROR", payload: response.message || "Failed to acquire lock" });
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.details || error.message || "Failed to acquire form lock";
      dispatch({ type: "SET_ERROR", payload: errorMessage });

      toast({
        title: "Lock Acquisition Failed",
        description: errorMessage,
        variant: "destructive",
      });

      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  }, [user, state.timerConfig.inactivityTimeout, toast]);

  // Release lock
  const releaseLock = useCallback(async (): Promise<boolean> => {
    if (!state.formId || !state.projectRef) {
      return false;
    }

    dispatch({ type: "SET_LOADING", payload: true });

    try {
      const response = await FormLockService.releaseLock({
        formId: state.formId,
        projectRef: state.projectRef,
      });

      if (response.success) {
        dispatch({ type: "SET_LOCK_STATE", payload: FormLockState.UNLOCKED });
        dispatch({ type: "SET_FORM_MODE", payload: FormLockMode.READ_ONLY });
        dispatch({ type: "CLEAR_LOCK_INFO" });
        dispatch({ type: "CLEAR_ERROR" });

        toast({
          title: "Form Lock Released",
          description: "The form is now available for others to edit.",
          variant: "default",
        });

        return true;
      } else {
        dispatch({ type: "SET_ERROR", payload: response.message || "Failed to release lock" });
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.details || error.message || "Failed to release form lock";
      dispatch({ type: "SET_ERROR", payload: errorMessage });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  }, [state.formId, state.projectRef, toast]);

  // Refresh lock
  const refreshLock = useCallback(async (): Promise<boolean> => {
    if (!state.formId || !state.projectRef || !user || !state.lockedByUser) {
      return false;
    }

    try {
      const response = await FormLockService.refreshLock({
        formId: state.formId,
        projectRef: state.projectRef,
        lockedByUserId: user.id,
      });

      if (response.success) {
        if (response.expiresAt) {
          dispatch({ type: "UPDATE_EXPIRY", payload: new Date(response.expiresAt) });
        }
        if (response.lastRefreshedAt) {
          dispatch({ type: "UPDATE_LAST_REFRESHED", payload: new Date(response.lastRefreshedAt) });
        }
        return true;
      } else {
        dispatch({ type: "SET_ERROR", payload: response.message || "Failed to refresh lock" });
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.details || error.message || "Failed to refresh form lock";
      dispatch({ type: "SET_ERROR", payload: errorMessage });
      return false;
    }
  }, [state.formId, state.projectRef, state.lockedByUser, user]);

  // Check lock status
  const checkLockStatus = useCallback(async (formId: string, projectRef: string): Promise<void> => {
    if (!user) return;

    dispatch({ type: "SET_LOADING", payload: true });
    dispatch({ type: "SET_FORM_INFO", payload: { formId, projectRef } });

    try {
      const status = await FormLockService.getLockStatus({ formId, projectRef });

      if (status?.isLocked) {
        if (status.lockedByUserId === user.id) {
          dispatch({ type: "SET_LOCK_STATE", payload: FormLockState.LOCKED_BY_CURRENT_USER });
          dispatch({ type: "SET_FORM_MODE", payload: FormLockMode.EDIT });
        } else {
          dispatch({ type: "SET_LOCK_STATE", payload: FormLockState.LOCKED_BY_OTHER_USER });
          dispatch({ type: "SET_FORM_MODE", payload: FormLockMode.READ_ONLY });
        }

        if (status.lockedByUserId && status.lockedByUserName && status.lockedByUserEmail) {
          dispatch({
            type: "SET_LOCK_INFO",
            payload: {
              lockedByUser: {
                id: status.lockedByUserId,
                name: status.lockedByUserName,
                email: status.lockedByUserEmail,
              },
              lockedAt: status.lockedAt ? new Date(status.lockedAt) : new Date(),
              expiresAt: status.expiresAt ? new Date(status.expiresAt) : new Date(),
              lastRefreshedAt: status.lastRefreshedAt ? new Date(status.lastRefreshedAt) : undefined,
            },
          });
        }
      } else {
        dispatch({ type: "SET_LOCK_STATE", payload: FormLockState.UNLOCKED });
        dispatch({ type: "SET_FORM_MODE", payload: FormLockMode.EDIT });
        dispatch({ type: "CLEAR_LOCK_INFO" });
      }
    } catch (error: any) {
      const errorMessage = (error as ErrorResponse).details || error.message || "Failed to check form lock status";
      dispatch({ type: "SET_ERROR", payload: errorMessage });
      // Default to unlocked state on error to allow access
      dispatch({ type: "SET_LOCK_STATE", payload: FormLockState.UNLOCKED });
      dispatch({ type: "SET_FORM_MODE", payload: FormLockMode.EDIT });
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  }, [user]);

  // Utility functions
  const isFormEditable = useCallback((): boolean => {
    return state.formMode === FormLockMode.EDIT && state.lockState === FormLockState.LOCKED_BY_CURRENT_USER;
  }, [state.formMode, state.lockState]);

  const isFormReadOnly = useCallback((): boolean => {
    return state.formMode === FormLockMode.READ_ONLY || state.lockState === FormLockState.LOCKED_BY_OTHER_USER;
  }, [state.formMode, state.lockState]);

  const canAcquireLock = useCallback((): boolean => {
    return state.lockState === FormLockState.UNLOCKED && !state.isLoading;
  }, [state.lockState, state.isLoading]);

  const getRemainingTime = useCallback((): number => {
    if (!state.expiresAt) return 0;
    return Math.max(0, state.expiresAt.getTime() - Date.now());
  }, [state.expiresAt]);

  const getWarningTime = useCallback((): number => {
    return state.timerConfig.warningTime;
  }, [state.timerConfig.warningTime]);

  // Timer controls
  const startRefreshTimer = useCallback(() => {
    stopRefreshTimer(); // Clear any existing timer

    if (state.lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
      refreshTimerRef.current = setInterval(() => {
        refreshLock().catch((error) => {
          console.error("Auto-refresh failed:", error);
        });
      }, state.timerConfig.refreshInterval);
    }
  }, [state.lockState, state.timerConfig.refreshInterval, refreshLock]);

  const stopRefreshTimer = useCallback(() => {
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
      refreshTimerRef.current = null;
    }
  }, []);

  const startInactivityTimer = useCallback(() => {
    stopInactivityTimer(); // Clear any existing timers

    if (state.lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
      // Set warning timer
      const warningDelay = state.timerConfig.inactivityTimeout - state.timerConfig.warningTime;
      warningTimerRef.current = setTimeout(() => {
        toast({
          title: "Inactivity Warning",
          description: `Your form lock will expire in ${Math.ceil(state.timerConfig.warningTime / 60000)} minutes due to inactivity.`,
          variant: "destructive",
        });
      }, warningDelay);

      // Set auto-release timer
      inactivityTimerRef.current = setTimeout(() => {
        releaseLock().then(() => {
          toast({
            title: "Form Lock Released",
            description: "Your form lock was released due to inactivity.",
            variant: "destructive",
          });
        });
      }, state.timerConfig.inactivityTimeout);
    }
  }, [state.lockState, state.timerConfig.inactivityTimeout, state.timerConfig.warningTime, releaseLock, toast]);

  const stopInactivityTimer = useCallback(() => {
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = null;
    }
    if (warningTimerRef.current) {
      clearTimeout(warningTimerRef.current);
      warningTimerRef.current = null;
    }
  }, []);

  const resetInactivityTimer = useCallback(() => {
    stopInactivityTimer();
    startInactivityTimer();
  }, [stopInactivityTimer, startInactivityTimer]);

  const contextValue: FormLockContextValue = useMemo(() => ({
    state,
    dispatch,
    acquireLock,
    releaseLock,
    refreshLock,
    checkLockStatus,
    isFormEditable,
    isFormReadOnly,
    canAcquireLock,
    getRemainingTime,
    getWarningTime,
    startRefreshTimer,
    stopRefreshTimer,
    startInactivityTimer,
    stopInactivityTimer,
    resetInactivityTimer,
  }), [
    state,
    dispatch,
    acquireLock,
    releaseLock,
    refreshLock,
    checkLockStatus,
    isFormEditable,
    isFormReadOnly,
    canAcquireLock,
    getRemainingTime,
    getWarningTime,
    startRefreshTimer,
    stopRefreshTimer,
    startInactivityTimer,
    stopInactivityTimer,
    resetInactivityTimer,
  ]);

  return (
    <FormLockContext.Provider value={contextValue}>
      {children}
    </FormLockContext.Provider>
  );
};

// Hook to use form lock context
export const useFormLockContext = (): FormLockContextValue => {
  const context = useContext(FormLockContext);
  if (!context) {
    throw new Error("useFormLockContext must be used within a FormLockProvider");
  }
  return context;
};
