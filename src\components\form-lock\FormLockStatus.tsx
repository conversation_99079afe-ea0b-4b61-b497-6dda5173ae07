import React from "react";
import { <PERSON>, <PERSON>, Lock<PERSON><PERSON>, User, Eye, RefreshCw } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useFormLock } from "@/hooks/useFormLock";
import { FormLockState, FormLockStatusProps } from "@/lib/types/form-lock";
import { cn } from "@/lib/utils";

/**
 * Component to display form lock status and provide lock-related actions
 */
export const FormLockStatus: React.FC<FormLockStatusProps> = ({
  showDetails = true,
  className,
  onViewReadOnly,
  showActions = true,
}) => {
  const {
    lockState,
    isLoading,
    error,
    lockedByUser,
    remainingTime,
    showInactivityWarning,
  } = useFormLock();

  // Format remaining time
  const formatRemainingTime = (timeMs: number): string => {
    if (timeMs <= 0) return "Expired";

    const minutes = Math.floor(timeMs / (1000 * 60));
    const seconds = Math.floor((timeMs % (1000 * 60)) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  // Get status color based on lock state
  const getStatusColor = (): string => {
    switch (lockState) {
      case FormLockState.LOCKED_BY_CURRENT_USER:
        return "bg-green-100 text-green-800 border-green-200";
      case FormLockState.LOCKED_BY_OTHER_USER:
        return "bg-red-100 text-red-800 border-red-200";
      case FormLockState.UNLOCKED:
        return "bg-gray-100 text-gray-800 border-gray-200";
      case FormLockState.ERROR:
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Get status icon
  const getStatusIcon = () => {
    switch (lockState) {
      case FormLockState.LOCKED_BY_CURRENT_USER:
        return <Lock className="h-4 w-4" />;
      case FormLockState.LOCKED_BY_OTHER_USER:
        return <Lock className="h-4 w-4" />;
      case FormLockState.UNLOCKED:
        return <LockOpen className="h-4 w-4" />;
      case FormLockState.ERROR:
        return <Lock className="h-4 w-4" />;
      default:
        return <LockOpen className="h-4 w-4" />;
    }
  };

  // Get status text
  const getStatusText = (): string => {
    switch (lockState) {
      case FormLockState.LOCKED_BY_CURRENT_USER:
        return "You are editing";
      case FormLockState.LOCKED_BY_OTHER_USER:
        return "Locked by another user";
      case FormLockState.UNLOCKED:
        return "Auto-acquiring lock...";
      case FormLockState.ERROR:
        return "Error checking lock status";
      default:
        return "Unknown status";
    }
  };

  // Handle view read-only
  const handleViewReadOnly = () => {
    onViewReadOnly?.();
  };



  // Show inactivity warning
  if (showInactivityWarning && lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
    return (
      <Alert className={cn("mb-4", className)}>
        <Clock className="h-4 w-4" />
        <AlertDescription>
          <span>
            Your editing session will expire in {formatRemainingTime(remainingTime)} due to inactivity.
            The session will be automatically extended when you interact with the form.
          </span>
        </AlertDescription>
      </Alert>
    );
  }

  // Show error state
  if (error) {
    return (
      <Alert variant="destructive" className={cn("mb-4", className)}>
        <AlertDescription>
          <span>Error: {error}. The system will automatically retry.</span>
        </AlertDescription>
      </Alert>
    );
  }

  // Main status display
  return (
    <Card className={cn("mb-4", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className={getStatusColor()}>
              {getStatusIcon()}
              <span className="ml-1">{getStatusText()}</span>
            </Badge>
            {isLoading && (
              <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
            )}
          </div>

          {showActions && lockState === FormLockState.LOCKED_BY_OTHER_USER && (
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleViewReadOnly}
                disabled={isLoading}
              >
                <Eye className="h-3 w-3 mr-1" />
                View Read-Only
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      {showDetails && (
        <CardContent className="pt-0">
          {lockState === FormLockState.LOCKED_BY_OTHER_USER && lockedByUser && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <User className="h-3 w-3" />
                <span>Currently being edited by:</span>
              </div>
              <div className="ml-5 text-sm">
                <div className="font-medium">{lockedByUser.name}</div>
                <div className="text-muted-foreground">{lockedByUser.email}</div>
              </div>
            </div>
          )}

          {lockState === FormLockState.LOCKED_BY_CURRENT_USER && remainingTime > 0 && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>Session expires in:</span>
              </div>
              <div className="ml-5 text-sm font-medium">
                {formatRemainingTime(remainingTime)}
              </div>
            </div>
          )}

          {lockState === FormLockState.UNLOCKED && (
            <div className="text-sm text-muted-foreground">
              The form lock is being automatically acquired. You can start editing once the lock is secured.
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default FormLockStatus;
