import {
  useMutation,
  useQuery,
  useQueryClient,
  UseMutationOptions,
  UseQueryOptions,
  QueryKey,
} from "@tanstack/react-query";
import { apiClient, ApiError } from "@/lib/api/api-client";
import { useToast } from "@/components/ui/use-toast";
import { ErrorResponse, QueryParams } from "@/lib/types/api";

/**
 * Custom hook for making API queries with React Query
 */
export function useApiQuery<TData = unknown, TError = ApiError>(
  queryKey: QueryKey,
  endpoint: string,
  params?: QueryParams,
  options?: Omit<
    UseQueryOptions<TData, TError, TData, QueryKey>,
    "queryKey" | "queryFn"
  >
) {
  const { toast } = useToast();

  return useQuery<TData, TError>({
    queryKey,
    queryFn: async () => {
      try {
        // Convert QueryParams to the format expected by apiClient
        const apiParams: Record<string, string | number | boolean | undefined> =
          {};
        if (params) {
          Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined) {
              if (Array.isArray(value)) {
                // Convert arrays to comma-separated strings
                apiParams[key] = value.join(",");
              } else {
                apiParams[key] = value;
              }
            }
          });
        }

        const response = await apiClient.get<TData>(endpoint, {
          params: apiParams,
        });
        return response.data;
      } catch (error) {
        const errorMessage =
          (error as ErrorResponse).details ?? "An error occurred";
        // Show toast for errors unless explicitly disabled
        // Check if toast should be suppressed
        if (options?.meta?.suppressToast !== true) {
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          });
        }
        throw error;
      }
    },
    ...options,
  });
}

/**
 * Custom hook for making API mutations with React Query
 */
export function useApiMutation<
  TData = unknown,
  TVariables = unknown,
  TError = ApiError,
  TContext = unknown
>(
  endpoint: string,
  method: "POST" | "PUT" | "PATCH" | "DELETE" = "POST",
  options?: Omit<
    UseMutationOptions<TData, TError, TVariables, TContext>,
    "mutationFn"
  >
) {
  // Get query client for invalidation in onSuccess
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<TData, TError, TVariables, TContext>({
    mutationFn: async (variables) => {
      try {
        let response;

        switch (method) {
          case "POST":
            response = await apiClient.post<TData>(endpoint, variables);
            break;
          case "PUT":
            response = await apiClient.put<TData>(endpoint, variables);
            break;
          case "PATCH":
            response = await apiClient.patch<TData>(endpoint, variables);
            break;
          case "DELETE":
            response = await apiClient.delete<TData>(endpoint);
            break;
        }

        return response.data;
      } catch (error) {
        const errorMessage =
          (error as ErrorResponse).details ?? "An error occurred";
        // Show toast for errors unless explicitly disabled
        if (!options?.meta?.suppressToast) {
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          });
        }
        throw error;
      }
    },
    // Show success toast by default and invalidate related queries
    onSuccess: (data, variables, context) => {
      if (!options?.meta?.suppressToast) {
        toast({
          title: "Success",
          description: "Operation completed successfully",
        });
      }

      // Invalidate related queries if specified
      if (options?.meta?.invalidateQueries) {
        queryClient.invalidateQueries({
          queryKey: options.meta.invalidateQueries as any,
        });
      }

      // Call the original onSuccess if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    ...options,
  });
}

/**
 * Custom hook for invalidating queries
 */
export function useInvalidateQueries() {
  const queryClient = useQueryClient();

  return {
    invalidateQueries: (queryKey: QueryKey) => {
      return queryClient.invalidateQueries({ queryKey });
    },
  };
}
