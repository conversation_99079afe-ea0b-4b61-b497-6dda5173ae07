import { useEffect, useState, useCallback, useRef } from "react";
import { useFormLockContext } from "@/contexts/FormLockContext";
import { useAuth } from "@/contexts/AuthContext";
import {
  UseFormLockOptions,
  UseFormLockReturn,
  FormLockState,
  FormLockMode,
  DEFAULT_FORM_LOCK_TIMER_CONFIG,
} from "@/lib/types/form-lock";

/**
 * Custom hook for managing form locks
 * Provides a simplified interface for components to interact with form locking
 */
export function useFormLock(
  options: UseFormLockOptions = {}
): UseFormLockReturn {
  const {
    formId: optionsFormId,
    projectRef: optionsProjectRef,
    autoRelease = true,
    timerConfig = {},
    onLockAcquired,
    onLockReleased,
    onLockFailed,
    onReadOnlyMode,
    onInactivityWarning,
  } = options;

  const { user } = useAuth();
  const {
    state,
    acquireLock: contextAcquireLock,
    releaseLock: contextReleaseLock,
    isFormEditable,
    isFormReadOnly,
    getRemainingTime,
    startRefreshTimer,
    stopRefreshTimer,
    startInactivityTimer,
    stopInactivityTimer,
    resetInactivityTimer,
  } = useFormLockContext();

  // Use formId and projectRef from options first, then fall back to context values
  const formId = optionsFormId || state.formId;
  const projectRef = optionsProjectRef || state.projectRef;

  // Internal release lock wrapper (for auto-release on unmount)
  const internalReleaseLock = useCallback(async (): Promise<boolean> => {
    const success = await contextReleaseLock();
    if (success) {
      setShowInactivityWarning(false);
      warningShownRef.current = false;
    }
    return success;
  }, [contextReleaseLock]);

  // Local state for inactivity warning
  const [showInactivityWarning, setShowInactivityWarning] = useState(false);

  // Refs for tracking
  const lastActivityRef = useRef<number>(Date.now());
  const warningShownRef = useRef<boolean>(false);

  // Refs for callbacks to prevent infinite loops
  const callbacksRef = useRef({
    onLockAcquired,
    onLockReleased,
    onLockFailed,
    onReadOnlyMode,
    onInactivityWarning,
  });

  // Update callback refs when they change
  useEffect(() => {
    callbacksRef.current = {
      onLockAcquired,
      onLockReleased,
      onLockFailed,
      onReadOnlyMode,
      onInactivityWarning,
    };
  });
  const mergedTimerConfig = {
    ...DEFAULT_FORM_LOCK_TIMER_CONFIG,
    ...timerConfig,
  };

  // Check if we should show inactivity warning
  useEffect(() => {
    const checkWarning = () => {
      const remainingTime = getRemainingTime();
      const warningThreshold = mergedTimerConfig.warningTime;

      if (
        remainingTime > 0 &&
        remainingTime <= warningThreshold &&
        !warningShownRef.current
      ) {
        setShowInactivityWarning(true);
        warningShownRef.current = true;
        callbacksRef.current.onInactivityWarning?.(remainingTime);
      } else if (remainingTime > warningThreshold) {
        setShowInactivityWarning(false);
        warningShownRef.current = false;
      }
    };

    const interval = setInterval(checkWarning, 1000); // Check every second
    return () => clearInterval(interval);
  }, [getRemainingTime, mergedTimerConfig.warningTime]);

  // Auto-acquire lock on mount (backend will handle checking if already locked)
  useEffect(() => {
    const autoAcquireLock = async () => {
      if (formId && projectRef && user) {
        // Directly attempt to acquire the lock
        // Backend will check if form is already locked and handle accordingly
        await acquireLock();
      }
    };

    autoAcquireLock();
  }, [formId, projectRef, user]); // eslint-disable-line react-hooks/exhaustive-deps

  // Auto-release lock on unmount if requested
  useEffect(() => {
    return () => {
      if (
        autoRelease &&
        state.lockState === FormLockState.LOCKED_BY_CURRENT_USER
      ) {
        // Use a timeout to ensure the release happens after component unmount
        setTimeout(() => {
          internalReleaseLock();
        }, 0);
      }
    };
  }, [autoRelease, state.lockState, internalReleaseLock]);

  // Handle lock state changes
  useEffect(() => {
    switch (state.lockState) {
      case FormLockState.LOCKED_BY_CURRENT_USER:
        startRefreshTimer();
        startInactivityTimer();
        break;

      case FormLockState.LOCKED_BY_OTHER_USER:
        stopRefreshTimer();
        stopInactivityTimer();
        break;

      case FormLockState.UNLOCKED:
        if (state.formMode === FormLockMode.READ_ONLY) {
        }
        stopRefreshTimer();
        stopInactivityTimer();
        break;

      case FormLockState.ERROR:
        stopRefreshTimer();
        stopInactivityTimer();
        break;
    }
  }, [
    state.lockState,
    state.formMode,
    state.error,
    startRefreshTimer,
    stopRefreshTimer,
    startInactivityTimer,
    stopInactivityTimer,
  ]);

  // Handle callbacks separately to avoid infinite loops
  useEffect(() => {
    if (state.lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
      callbacksRef.current.onLockAcquired?.();
    }
  }, [state.lockState]);

  useEffect(() => {
    if (state.lockState === FormLockState.LOCKED_BY_OTHER_USER) {
      callbacksRef.current.onReadOnlyMode?.();
    }
  }, [state.lockState]);

  useEffect(() => {
    if (
      state.lockState === FormLockState.UNLOCKED &&
      state.formMode === FormLockMode.READ_ONLY
    ) {
      callbacksRef.current.onLockReleased?.();
    }
  }, [state.lockState, state.formMode]);

  useEffect(() => {
    if (state.lockState === FormLockState.ERROR) {
      callbacksRef.current.onLockFailed?.(
        state.error || "Unknown error occurred"
      );
    }
  }, [state.lockState, state.error]);

  // Activity tracking
  const trackActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    if (state.lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
      resetInactivityTimer();
      setShowInactivityWarning(false);
      warningShownRef.current = false;
    }
  }, [state.lockState, resetInactivityTimer]);

  // Acquire lock wrapper
  const acquireLock = useCallback(async (): Promise<boolean> => {
    if (!formId || !projectRef) {
      console.warn("Cannot acquire lock: formId or projectRef is missing");
      return false;
    }

    const success = await contextAcquireLock(formId, projectRef);
    if (success) {
      trackActivity();
    }
    return success;
  }, [formId, projectRef, contextAcquireLock, trackActivity]);

  // Reset inactivity timer wrapper
  const resetInactivityTimerWrapper = useCallback(() => {
    trackActivity();
  }, [trackActivity]);

  // Set up activity listeners
  useEffect(() => {
    const events = [
      "mousedown",
      "mousemove",
      "keypress",
      "scroll",
      "touchstart",
      "click",
    ];

    const handleActivity = () => {
      trackActivity();
    };

    // Add event listeners
    events.forEach((event) => {
      document.addEventListener(event, handleActivity, true);
    });

    // Cleanup
    return () => {
      events.forEach((event) => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [trackActivity]);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        // Page became visible, attempt to acquire lock again
        // (will be no-op if already locked by current user)
        if (formId && projectRef) {
          acquireLock();
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [formId, projectRef]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle beforeunload to release lock
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (state.lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
        // Use navigator.sendBeacon for reliable cleanup
        if (navigator.sendBeacon && formId && projectRef) {
          const data = JSON.stringify({ formId, projectRef });
          navigator.sendBeacon("/api/v1/form-lock/release", data);
        }
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [state.lockState, formId, projectRef]);

  return {
    lockState: state.lockState,
    formMode: state.formMode,
    isEditable: isFormEditable(),
    isReadOnly: isFormReadOnly(),
    isLoading: state.isLoading,
    error: state.error,
    lockedByUser: state.lockedByUser,
    remainingTime: getRemainingTime(),
    showInactivityWarning,
    resetInactivityTimer: resetInactivityTimerWrapper,
  };
}
