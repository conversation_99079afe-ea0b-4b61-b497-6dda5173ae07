import { useState, useEffect, useMemo, useCallback } from "react";
import { useLocation } from "react-router-dom";
import {
  FileText,
  Users,
  Settings,
  FolderOpen,
  DollarSign,
  BarChart3,
  Home,
  Shield,
  Database,
  Calendar,
  Mail,
  Bell
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { ErrorResponse, GlobalPermissions, NavigationItem } from "@/lib/types/api";
import { NavItem, NavigationState, IconMapping } from "@/lib/types/navigation";
import { PermissionStatus } from "@/lib/types/api";

/**
 * Icon mapping for backend navigation items
 * Maps backend icon identifiers to React components
 */
const ICON_MAPPING: IconMapping = {
  "file-text": <FileText className="h-5 w-5" />,
  "users": <Users className="h-5 w-5" />,
  "settings": <Settings className="h-5 w-5" />,
  "folder-open": <FolderOpen className="h-5 w-5" />,
  "dollar-sign": <DollarSign className="h-5 w-5" />,
  "bar-chart-3": <BarChart3 className="h-5 w-5" />,
  "home": <Home className="h-5 w-5" />,
  "shield": <Shield className="h-5 w-5" />,
  "database": <Database className="h-5 w-5" />,
  "calendar": <Calendar className="h-5 w-5" />,
  "mail": <Mail className="h-5 w-5" />,
  "bell": <Bell className="h-5 w-5" />,
};


// Static config for actual navigation entries
export const navConfig: Omit<NavigationItem, 'permissions'>[] = [
  { id: "projects", name: "Projects", href: "/projects", icon: "file-text", order: 1 },
  { id: "funding-round", name: "Funding Rounds", href: "/funding-rounds", icon: "file-text", order: 2 },
  { id: "forms", name: "Form Builder", href: "/forms", icon: "file-text", order: 3 },
  { id: "users", name: "Users", href: "/users", icon: "file-text", order: 4 }
];

function mapPermissionsToNavigation(globalPermissions: GlobalPermissions | null): NavigationItem[] {
  if (!globalPermissions) return [];
  return navConfig.map(item => {
    const perms = globalPermissions[item.id];
    const unothorizedPerm: PermissionStatus = "UNAUTHORIZED" as PermissionStatus
    const viewPermission: PermissionStatus = perms?.list ?? perms?.create ?? unothorizedPerm;

    return {
      ...item,
      permissions: {
        view: viewPermission
      }
    };
  });
}

/**
 * Convert backend navigation item to frontend nav item
 */
const convertToNavItem = (
  item: NavigationItem,
  currentPath: string,
  hasPermission: boolean
): NavItem => ({
  id: item.id,
  name: item.name,
  href: item.href,
  icon: ICON_MAPPING[item.icon] || <FileText className="h-5 w-5" />, // Fallback icon
  active: currentPath.startsWith(item.href),
  order: item.order,
  parentId: item.parentId,
  hasPermission,
});

/**
 * Hook for backend-driven navigation with fallback to client-side navigation
 */
export function useBackendNavigation() {
  const location = useLocation();
  const { isAuthenticated, user, hasNavigationPermission, globalPermissions } = useAuth();

  const [navigationState, setNavigationState] = useState<NavigationState>({
    items: [],
    isLoading: true,
    error: null,
    lastFetched: null,
  });

  /**
   * Fetch navigation items from backend
   */
  const fetchNavigation = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setNavigationState({
        items: [],
        isLoading: false,
        error: null,
        lastFetched: null,
      });
      return;
    }

    setNavigationState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log("🔄 Fetching navigation from backend");
      const navigationItems = mapPermissionsToNavigation(globalPermissions);
      // Convert backend items to frontend nav items with permission checking
      const navItems = navigationItems
        .map(item => {
          const hasPermission = hasNavigationPermission(item.id);
          return convertToNavItem(item, location.pathname, hasPermission);
        })
        .filter(item => item.hasPermission) // Only show items user has permission for
        .sort((a, b) => a.order - b.order); // Sort by order

      setNavigationState({
        items: navItems,
        isLoading: false,
        error: null,
        lastFetched: new Date(),
      });

      console.log("✅ Navigation loaded successfully:", navItems.length, "items");
    } catch (error) {
      console.error("❌ Error fetching navigation:", error);
      const errorMessage = (error as ErrorResponse).details ?? "Failed to load navigation";
      setNavigationState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, [isAuthenticated, user, hasNavigationPermission, location.pathname]);

  /**
   * Refresh navigation data
   */
  const refreshNavigation = useCallback(async () => {
    await fetchNavigation();
  }, [fetchNavigation]);

  // Fetch navigation when user or permissions change
  useEffect(() => {
    fetchNavigation();
  }, [fetchNavigation, globalPermissions]);

  // Memoized navigation items with active state updates
  const navItems = useMemo(() => {
    return navigationState.items.map(item => ({
      ...item,
      active: location.pathname.startsWith(item.href),
    }));
  }, [navigationState.items, location.pathname]);

  return {
    navItems,
    isLoading: navigationState.isLoading,
    error: navigationState.error,
    lastFetched: navigationState.lastFetched,
    refreshNavigation,
  };
}

/**
 * Hook that provides navigation with fallback strategy
 * Uses backend navigation if available, falls back to client-side navigation
 */
export function useNavigationWithFallback() {
  const backendNav = useBackendNavigation();

  // If backend navigation fails or is empty, we could implement fallback logic here
  // For now, we'll just return the backend navigation
  return backendNav;
}
