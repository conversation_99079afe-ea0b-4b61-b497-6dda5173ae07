import { apiClient } from "@/lib/api/api-client";

/**
 * Form lock request payload for acquiring a lock
 */
export interface FormLockRequest {
  formId: string;
  projectRef: string;
}

/**
 * Form lock refresh request payload
 */
export interface FormLockRefreshRequest extends FormLockRequest {
  lockedByUserId: string;
}

/**
 * Form lock status response
 */
export interface FormLockStatus {
  formId: string;
  projectRef: string;
  isLocked: boolean;
  lockedByUserId?: string;
  lockedByUserName?: string;
  lockedByUserEmail?: string;
  lockedAt?: string;
  expiresAt?: string;
  lastRefreshedAt?: string;
}

/**
 * Form lock data from API response
 */
export interface FormLockData {
  id: string;
  formId: string;
  projectRef: string;
  lockedByUserId: string;
  lockedAt: string;
  username?: string | null;
  expiresAt?: string;
}

/**
 * Form lock acquisition response (wrapped in ApiResponse)
 */
export interface FormLockAcquisitionResponse {
  success: boolean;
  lockId?: string;
  message?: string;
  lockedByUserId?: string;
  lockedByUserName?: string;
  lockedByUserEmail?: string;
  lockedAt?: string;
  expiresAt?: string;
}

/**
 * Form lock release response
 */
export interface FormLockReleaseResponse {
  success: boolean;
  message?: string;
}

/**
 * Form lock refresh response
 */
export interface FormLockRefreshResponse {
  success: boolean;
  message?: string;
  expiresAt?: string;
  lastRefreshedAt?: string;
}

/**
 * Service for managing form locks
 * Handles acquiring, releasing, refreshing, and checking form lock status
 */
export const FormLockService = {
  /**
   * Acquire a lock on a form for editing
   * @param request Form lock request containing formId and projectRef
   * @returns Promise resolving to lock acquisition response
   */
  acquireLock: async (
    request: FormLockRequest
  ): Promise<FormLockAcquisitionResponse> => {
    try {
      const response = await apiClient.post<FormLockData>(
        "/form-lock",
        request
      );

      // Transform the API response to match the expected interface
      const lockData = response.data;
      return {
        success: true,
        lockId: lockData.id,
        lockedByUserId: lockData.lockedByUserId,
        lockedByUserName: lockData.username || undefined,
        lockedByUserEmail: undefined, // Not provided by API
        lockedAt: lockData.lockedAt,
        expiresAt: lockData.expiresAt,
        message: "Lock acquired successfully",
      };
    } catch (error) {
      console.error("Error acquiring form lock:", error);
      throw error;
    }
  },

  /**
   * Release a lock on a form
   * @param request Form lock request containing formId and projectRef
   * @returns Promise resolving to lock release response
   */
  releaseLock: async (
    request: FormLockRequest
  ): Promise<FormLockReleaseResponse> => {
    try {
      const response = await apiClient.delete<any>("/form-lock", {
        params: {
          formId: request.formId,
          projectRef: request.projectRef,
        },
      });

      // Transform the API response to match the expected interface
      return {
        success: true,
        message: response.data?.message || "Lock released successfully",
      };
    } catch (error) {
      console.error("Error releasing form lock:", error);
      throw error;
    }
  },

  /**
   * Get the current lock status of a form
   * @param request Form lock request containing formId and projectRef
   * @returns Promise resolving to form lock status
   */
  getLockStatus: async (
    request: FormLockRequest
  ): Promise<FormLockStatus | null> => {
    try {
      const response = await apiClient.get<FormLockData | null>("/form-lock", {
        params: {
          formId: request.formId,
          projectRef: request.projectRef,
        },
      });

      // If no lock data, return null
      if (!response.data) {
        return null;
      }

      // Transform the API response to match the expected interface
      const lockData = response.data;
      return {
        formId: lockData.formId,
        projectRef: lockData.projectRef,
        isLocked: true, // If we get data, it means it's locked
        lockedByUserId: lockData.lockedByUserId,
        lockedByUserName: lockData.username || undefined,
        lockedByUserEmail: undefined, // Not provided by API
        lockedAt: lockData.lockedAt,
        expiresAt: lockData.expiresAt,
        lastRefreshedAt: undefined, // Not provided by API
      };
    } catch (error) {
      console.error("Error getting form lock status:", error);
      // Return null instead of throwing to indicate no lock exists
      return null;
    }
  },

  /**
   * Refresh an existing lock to extend its expiration time
   * @param request Form lock refresh request containing formId, projectRef, and lockedByUserId
   * @returns Promise resolving to lock refresh response
   */
  refreshLock: async (
    request: FormLockRefreshRequest
  ): Promise<FormLockRefreshResponse> => {
    try {
      const response = await apiClient.put<FormLockData>("/form-lock", request);

      // Transform the API response to match the expected interface
      const lockData = response.data;
      return {
        success: true,
        message: "Lock refreshed successfully",
        expiresAt: lockData.expiresAt,
        lastRefreshedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error refreshing form lock:", error);
      throw error;
    }
  },

  /**
   * Check if a form is currently locked by another user
   * @param request Form lock request containing formId and projectRef
   * @param currentUserId Current user's ID to compare against lock owner
   * @returns Promise resolving to boolean indicating if form is locked by another user
   */
  isLockedByOtherUser: async (
    request: FormLockRequest,
    currentUserId: string
  ): Promise<boolean> => {
    try {
      const status = await FormLockService.getLockStatus(request);
      return Boolean(
        status?.isLocked && status.lockedByUserId !== currentUserId
      );
    } catch (error) {
      console.error("Error checking if form is locked by other user:", error);
      // In case of error, assume form is not locked to allow access
      return false;
    }
  },

  /**
   * Check if a form is currently locked by the current user
   * @param request Form lock request containing formId and projectRef
   * @param currentUserId Current user's ID to compare against lock owner
   * @returns Promise resolving to boolean indicating if form is locked by current user
   */
  isLockedByCurrentUser: async (
    request: FormLockRequest,
    currentUserId: string
  ): Promise<boolean> => {
    try {
      const status = await FormLockService.getLockStatus(request);
      return Boolean(
        status?.isLocked && status.lockedByUserId === currentUserId
      );
    } catch (error) {
      console.error("Error checking if form is locked by current user:", error);
      return false;
    }
  },

  /**
   * Attempt to acquire a lock with retry logic
   * @param request Form lock request containing formId and projectRef
   * @param maxRetries Maximum number of retry attempts (default: 3)
   * @param retryDelay Delay between retries in milliseconds (default: 1000)
   * @returns Promise resolving to lock acquisition response
   */
  acquireLockWithRetry: async (
    request: FormLockRequest,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<FormLockAcquisitionResponse> => {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await FormLockService.acquireLock(request);
      } catch (error) {
        lastError = error;

        if (attempt < maxRetries) {
          console.warn(
            `Lock acquisition attempt ${
              attempt + 1
            } failed, retrying in ${retryDelay}ms...`
          );
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      }
    }

    throw lastError;
  },
};
