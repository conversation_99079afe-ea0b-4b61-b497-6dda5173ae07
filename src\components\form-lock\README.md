# Form Lock System

The Form Lock System prevents multiple users from editing the same application form simultaneously, ensuring data integrity while allowing read-only access for other users.

## Features

- **Exclusive Editing**: Only one user can edit a form at a time
- **Read-Only Access**: Other users can view the form in read-only mode
- **Automatic Lock Management**: Locks are automatically acquired, refreshed, and released
- **Inactivity Timeout**: Locks are released after 10 minutes of inactivity
- **Visual Feedback**: Clear UI indicators for lock status
- **Error Handling**: Graceful handling of network errors and edge cases

## Architecture

### Core Components

1. **FormLockService** (`src/lib/services/form-lock-service.ts`)

   - Handles all API interactions for form locks
   - Provides methods for acquire, release, refresh, and status check

2. **FormLockContext** (`src/contexts/FormLockContext.tsx`)

   - Global state management for form locks
   - Timer management for auto-refresh and inactivity
   - Toast notifications for user feedback

3. **useFormLock Hook** (`src/hooks/useFormLock.ts`)

   - Simplified interface for components
   - Activity tracking and timer management
   - Automatic cleanup on navigation

4. **FormLockStatus Component** (`src/components/form-lock/FormLockStatus.tsx`)

   - Visual status indicator
   - Action buttons for lock operations
   - User information display

5. **ReadOnlyFormWrapper** (`src/components/form-lock/ReadOnlyFormWrapper.tsx`)
   - Disables form inputs when locked by another user
   - Maintains visual consistency
   - Provides read-only styling

## API Endpoints

The system uses the following API endpoints:

- `POST /api/v1/form-lock` - Acquire a form lock
- `DELETE /api/v1/form-lock` - Release a form lock
- `GET /api/v1/form-lock` - Get lock status
- `PUT /api/v1/form-lock` - Refresh an existing lock

## Usage

### Basic Integration

```tsx
import { FormLockProvider } from "@/contexts/FormLockContext";
import { useFormLock } from "@/hooks/useFormLock";
import { FormLockStatus } from "@/components/form-lock/FormLockStatus";
import { ReadOnlyFormWrapper } from "@/components/form-lock/ReadOnlyFormWrapper";

// Wrap your app with FormLockProvider
function App() {
  return (
    <FormLockProvider>
      <YourFormComponent />
    </FormLockProvider>
  );
}

// Use in your form component
function YourFormComponent() {
  const { isEditable, isReadOnly, acquireLock, releaseLock } = useFormLock({
    formId: "your-form-id",
    projectRef: "your-project-ref",
    autoAcquire: false,
    autoRelease: true,
  });

  return (
    <div>
      <FormLockStatus showDetails={true} showActions={true} />

      <ReadOnlyFormWrapper>
        <form>{/* Your form content */}</form>
      </ReadOnlyFormWrapper>
    </div>
  );
}
```

### Hook Options

```tsx
const formLock = useFormLock({
  formId: "form-123",
  projectRef: "project-456",
  autoAcquire: false, // Don't auto-acquire on mount
  autoRelease: true, // Auto-release on unmount
  onLockAcquired: () => {
    console.log("Lock acquired successfully");
  },
  onLockFailed: (error) => {
    console.error("Failed to acquire lock:", error);
  },
  onReadOnlyMode: () => {
    console.log("Form is now in read-only mode");
  },
  onInactivityWarning: (remainingTime) => {
    console.log(`Lock expires in ${remainingTime}ms`);
  },
});
```

## Timer Configuration

Default timer settings:

- **Refresh Interval**: 5 minutes - How often to refresh active locks
- **Inactivity Timeout**: 10 minutes - When to release locks due to inactivity
- **Warning Time**: 2 minutes - When to show inactivity warning
- **Retry Interval**: 30 seconds - Delay between retry attempts

These can be customized via environment variables or timer config:

```tsx
const customTimerConfig = {
  refreshInterval: 3 * 60 * 1000, // 3 minutes
  inactivityTimeout: 15 * 60 * 1000, // 15 minutes
  warningTime: 3 * 60 * 1000, // 3 minutes
  retryInterval: 45 * 1000, // 45 seconds
};
```

## Lock States

The system manages several lock states:

- `UNLOCKED` - Form is available for editing
- `LOCKED_BY_CURRENT_USER` - Current user has the lock
- `LOCKED_BY_OTHER_USER` - Another user has the lock
- `ACQUIRING` - Lock acquisition in progress
- `RELEASING` - Lock release in progress
- `ERROR` - Error occurred during lock operation

## Form Modes

- `EDIT` - Form is editable by current user
- `READ_ONLY` - Form is read-only (locked by another user)
- `LOADING` - Lock status is being determined

## Error Handling

The system handles various error scenarios:

1. **Network Errors**: Retries with exponential backoff
2. **Lock Conflicts**: Shows user-friendly messages
3. **Session Expiry**: Automatically refreshes authentication
4. **Server Errors**: Graceful degradation to unlocked state

## Testing

Run the test suite:

```bash
npm test src/components/form-lock/__tests__/
```

The tests cover:

- Lock acquisition and release
- Status checking
- Read-only mode functionality
- Error handling
- Timer management
- Component integration

## Integration Points

### Project List

The "Edit Application" button in the projects list checks lock status before navigation:

```tsx
// In entity-config-registry.ts
handler: async (row) => {
  // Check lock status first
  const lockStatus = await apiClient.get(
    `/form-lock/status?formId=${formId}&projectRef=${projectRef}`
  );

  if (lockStatus.data.isLocked) {
    // Show confirmation dialog
    const proceed = confirm(
      "Form is locked by another user. View in read-only mode?"
    );
    if (!proceed) return;
  }

  // Navigate to form
  window.location.href = `/applications/${formId}/submit/${projectRef}`;
};
```

### Form Submission Page

The FormSubmissionPage is wrapped with FormLockProvider and includes:

- Lock status checking on mount
- FormLockStatus component for user feedback
- ReadOnlyFormWrapper for input disabling
- Automatic lock management

## Best Practices

1. **Always wrap forms with FormLockProvider** at the application level
2. **Use autoRelease: true** to ensure locks are cleaned up
3. **Check lock status** before allowing form editing
4. **Provide clear user feedback** about lock status
5. **Handle errors gracefully** with fallback to unlocked state
6. **Test edge cases** like network failures and concurrent access

## Troubleshooting

### Common Issues

1. **Locks not releasing**: Check that autoRelease is enabled and beforeunload handlers are working
2. **Timer not working**: Verify that the component stays mounted and timers aren't being cleared
3. **Read-only mode not working**: Ensure ReadOnlyFormWrapper is properly wrapping form elements
4. **API errors**: Check network connectivity and API endpoint availability

### Debug Mode

Enable debug logging by setting localStorage:

```javascript
localStorage.setItem("form-lock-debug", "true");
```

This will log all lock operations to the console for debugging purposes.
