# Form Lock Feature - Quick Reference Guide

## 🚀 Quick Start

### 1. Basic Integration (5 minutes)

```tsx
// 1. Wrap your app with FormLockProvider
import { FormLockProvider } from "@/contexts/FormLockContext";

function App() {
  return (
    <FormLockProvider>
      <YourApp />
    </FormLockProvider>
  );
}

// 2. Use in your form component
import { useFormLock } from "@/hooks/useFormLock";
import { FormLockStatus } from "@/components/form-lock/FormLockStatus";
import { ReadOnlyFormWrapper } from "@/components/form-lock/ReadOnlyFormWrapper";

function YourFormPage() {
  const { isReadOnly } = useFormLock({
    formId: "your-form-id",
    projectRef: "your-project-ref",
  });

  return (
    <div>
      <FormLockStatus showDetails={true} showActions={true} />
      <ReadOnlyFormWrapper>
        <YourFormContent />
      </ReadOnlyFormWrapper>
    </div>
  );
}
```

## 📋 API Endpoints Checklist

| Endpoint            | Method | Status | Description         |
| ------------------- | ------ | ------ | ------------------- |
| `/api/v1/form-lock` | POST   | ⏳     | Acquire form lock   |
| `/api/v1/form-lock` | DELETE | ⏳     | Release form lock   |
| `/api/v1/form-lock` | GET    | ⏳     | Get lock status     |
| `/api/v1/form-lock` | PUT    | ⏳     | Refresh lock expiry |

## 🎯 Key Features

✅ **Exclusive Editing** - Only one user can edit at a time  
✅ **Read-Only Mode** - Others can view form while locked  
✅ **Auto-Refresh** - Locks refresh every 5 minutes  
✅ **Inactivity Timeout** - Auto-release after 10 minutes  
✅ **Visual Feedback** - Clear UI indicators and notifications  
✅ **Error Handling** - Graceful fallbacks and retry logic

## 🔧 Configuration Options

### Timer Settings

```typescript
const timerConfig = {
  refreshInterval: 5 * 60 * 1000, // 5 minutes
  inactivityTimeout: 10 * 60 * 1000, // 10 minutes
  warningTime: 2 * 60 * 1000, // 2 minutes
  retryInterval: 30 * 1000, // 30 seconds
};
```

### Hook Options

```typescript
const formLock = useFormLock({
  formId: "form-123",
  projectRef: "project-456",
  autoRelease: true, // Auto-release on unmount (default: true)
  onLockAcquired: () => console.log("Lock acquired"),
  onLockFailed: (error) => console.error("Lock failed"),
  onReadOnlyMode: () => console.log("Read-only mode"),
  onInactivityWarning: (time) => console.log(`${time}ms remaining`),
});
```

**Note**: Lock acquisition is now automatic and simplified - the hook directly attempts to acquire the lock on mount. The backend handles checking if the form is already locked.

## 🎨 UI Components

### FormLockStatus Component

```tsx
<FormLockStatus
  showDetails={true} // Show lock details
  showActions={true} // Show action buttons
  onViewReadOnly={() => {}} // Custom read-only handler
  onTryAcquire={() => {}} // Custom acquire handler
/>
```

### ReadOnlyFormWrapper

```tsx
<ReadOnlyFormWrapper
  showOverlay={true} // Show visual overlay
  readOnlyMessage="Custom message"
>
  <YourFormContent />
</ReadOnlyFormWrapper>
```

## 🔍 Lock States

| State                    | Description                  | UI Indicator       |
| ------------------------ | ---------------------------- | ------------------ |
| `UNLOCKED`               | Available for editing        | 🟢 Green badge     |
| `LOCKED_BY_CURRENT_USER` | You are editing              | 🔵 Blue badge      |
| `LOCKED_BY_OTHER_USER`   | Locked by another user       | 🔴 Red badge       |
| `ACQUIRING`              | Lock acquisition in progress | ⏳ Loading spinner |
| `RELEASING`              | Lock release in progress     | ⏳ Loading spinner |
| `ERROR`                  | Error occurred               | ⚠️ Warning badge   |

## 🛠️ Common Use Cases

### 1. Check if form is editable

```typescript
const { isEditable, isReadOnly } = useFormLock({ formId, projectRef });

if (isEditable) {
  // Show edit controls
} else if (isReadOnly) {
  // Show read-only message
}
```

### 2. Automatic lock behavior

```typescript
const { isEditable, isReadOnly, lockState } = useFormLock({
  formId,
  projectRef,
});

// Lock is automatically acquired on mount
// Backend handles checking if form is already locked
// No manual lock operations or status checks needed

// Check lock state
if (lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
  // User can edit the form
} else if (lockState === FormLockState.LOCKED_BY_OTHER_USER) {
  // Form is read-only
} else if (lockState === FormLockState.UNLOCKED) {
  // Lock acquisition is in progress
}
```

### 3. Activity tracking

```typescript
const { resetInactivityTimer } = useFormLock({ formId, projectRef });

// Reset timer on user activity
const handleUserActivity = () => {
  resetInactivityTimer();
};
```

## 🚨 Error Handling

### Network Errors

```typescript
// Automatic retry with exponential backoff
const { error } = useFormLock({ formId, projectRef });

if (error) {
  // Show error message to user
  console.error("Form lock error:", error);
}
```

### Fallback Strategy

- **API Unavailable**: Default to unlocked state
- **Invalid Response**: Show user-friendly error message
- **Authentication Issues**: Redirect to login
- **Timer Failures**: Graceful degradation

## 📊 Monitoring

### Debug Mode

```javascript
// Enable debug logging
localStorage.setItem("form-lock-debug", "true");
```

### Key Metrics

- Lock acquisition success rate
- Average lock duration
- Conflict frequency
- Timeout rate
- Error rates

## 🧪 Testing

### Unit Tests

```bash
npm test src/components/form-lock/__tests__/
```

### Manual Testing Scenarios

1. **Single User**: Acquire → Edit → Release
2. **Multiple Users**: User A locks → User B sees read-only
3. **Inactivity**: Lock → Wait 10 minutes → Auto-release
4. **Network Issues**: Disconnect → Reconnect → Resume
5. **Browser Close**: Lock → Close browser → Lock expires

## 🔧 Troubleshooting

### Common Issues

**🐛 Locks not releasing on browser close**

```typescript
// Ensure beforeunload handler is working
useEffect(() => {
  const handleBeforeUnload = () => {
    if (lockState === FormLockState.LOCKED_BY_CURRENT_USER) {
      navigator.sendBeacon(
        "/api/v1/form-lock/release",
        JSON.stringify({ formId, projectRef })
      );
    }
  };

  window.addEventListener("beforeunload", handleBeforeUnload);
  return () => window.removeEventListener("beforeunload", handleBeforeUnload);
}, [lockState, formId, projectRef]);
```

**🐛 Users stuck in read-only mode**

```typescript
// Check lock expiration and provide manual override
const { checkStatus } = useFormLock({ formId, projectRef });

// Refresh status periodically
useEffect(() => {
  const interval = setInterval(checkStatus, 30000); // Every 30 seconds
  return () => clearInterval(interval);
}, [checkStatus]);
```

**🐛 Timer memory leaks**

```typescript
// Ensure proper cleanup
useEffect(() => {
  return () => {
    // Cleanup timers on unmount
    clearInterval(refreshTimer);
    clearTimeout(inactivityTimer);
  };
}, []);
```

## 📚 Additional Resources

- **Full Documentation**: `docs/form-lock-feature.md`
- **Component README**: `src/components/form-lock/README.md`
- **API Service**: `src/lib/services/form-lock-service.ts`
- **Type Definitions**: `src/lib/types/form-lock.ts`
- **Test Suite**: `src/components/form-lock/__tests__/`

## 🎯 Production Checklist

### Backend

- [ ] API endpoints implemented and tested
- [ ] Database tables created
- [ ] Authentication/authorization configured
- [ ] Rate limiting enabled
- [ ] Monitoring/logging set up

### Frontend

- [ ] FormLockProvider wrapped at app level
- [ ] All form pages integrated
- [ ] Timer configurations validated
- [ ] Error handling tested
- [ ] Cross-browser compatibility verified

### Testing

- [ ] Unit tests passing
- [ ] Integration tests completed
- [ ] Multi-user scenarios tested
- [ ] Network failure scenarios tested
- [ ] Performance benchmarks met

## 🚀 Deployment

1. **Deploy Backend**: Ensure all 4 API endpoints are live
2. **Deploy Frontend**: Update with FormLock integration
3. **Test Production**: Verify with real users
4. **Monitor**: Watch for errors and performance issues
5. **Iterate**: Gather feedback and improve

---

**Need Help?** Check the full documentation or create an issue in the project repository.
