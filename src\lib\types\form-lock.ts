/**
 * Form lock state enumeration
 */
export enum FormLockState {
  UNLOCKED = "unlocked",
  LOCKED_BY_CURRENT_USER = "locked_by_current_user",
  LOCKED_BY_OTHER_USER = "locked_by_other_user",
  ACQUIRING = "acquiring",
  RELEASING = "releasing",
  ERROR = "error",
}

/**
 * Form lock mode enumeration
 */
export enum FormLockMode {
  EDIT = "edit",
  READ_ONLY = "read_only",
  LOADING = "loading",
}

/**
 * Form lock timer configuration
 */
export interface FormLockTimerConfig {
  /** Interval for refreshing the lock in milliseconds (default: 5 minutes) */
  refreshInterval: number;
  /** Timeout for inactivity before releasing lock in milliseconds (default: 10 minutes) */
  inactivityTimeout: number;
  /** Warning time before auto-release in milliseconds (default: 2 minutes) */
  warningTime: number;
  /** Retry interval for failed operations in milliseconds (default: 30 seconds) */
  retryInterval: number;
}

/**
 * Form lock context state
 */
export interface FormLockContextState {
  /** Current lock state */
  lockState: FormLockState;
  /** Current form mode (edit/read-only) */
  formMode: FormLockMode;
  /** Form ID being locked */
  formId?: string;
  /** Project reference */
  projectRef?: string;
  /** User who currently holds the lock */
  lockedByUser?: {
    id: string;
    name: string;
    email: string;
  };
  /** When the lock was acquired */
  lockedAt?: Date;
  /** When the lock expires */
  expiresAt?: Date;
  /** Last time the lock was refreshed */
  lastRefreshedAt?: Date;
  /** Error message if any */
  error?: string;
  /** Whether a lock operation is in progress */
  isLoading: boolean;
  /** Timer configuration */
  timerConfig: FormLockTimerConfig;
}

/**
 * Form lock context actions
 */
export type FormLockAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_LOCK_STATE"; payload: FormLockState }
  | { type: "SET_FORM_MODE"; payload: FormLockMode }
  | { type: "SET_FORM_INFO"; payload: { formId: string; projectRef: string } }
  | {
      type: "SET_LOCK_INFO";
      payload: {
        lockedByUser: { id: string; name: string; email: string };
        lockedAt: Date;
        expiresAt: Date;
        lastRefreshedAt?: Date;
      };
    }
  | { type: "CLEAR_LOCK_INFO" }
  | { type: "SET_ERROR"; payload: string }
  | { type: "CLEAR_ERROR" }
  | { type: "UPDATE_EXPIRY"; payload: Date }
  | { type: "UPDATE_LAST_REFRESHED"; payload: Date }
  | { type: "RESET" };

/**
 * Form lock context value
 */
export interface FormLockContextValue {
  state: FormLockContextState;
  dispatch: React.Dispatch<FormLockAction>;

  // Lock operations
  acquireLock: (formId: string, projectRef: string) => Promise<boolean>;
  releaseLock: () => Promise<boolean>;
  refreshLock: () => Promise<boolean>;
  checkLockStatus: (formId: string, projectRef: string) => Promise<void>;

  // Utility functions
  isFormEditable: () => boolean;
  isFormReadOnly: () => boolean;
  canAcquireLock: () => boolean;
  getRemainingTime: () => number;
  getWarningTime: () => number;

  // Timer controls
  startRefreshTimer: () => void;
  stopRefreshTimer: () => void;
  startInactivityTimer: () => void;
  stopInactivityTimer: () => void;
  resetInactivityTimer: () => void;
}

/**
 * Form lock hook options
 */
export interface UseFormLockOptions {
  /** Form ID to lock */
  formId?: string;
  /** Project reference */
  projectRef?: string;

  /** Whether to automatically release lock on unmount */
  autoRelease?: boolean;
  /** Custom timer configuration */
  timerConfig?: Partial<FormLockTimerConfig>;
  /** Callback when lock is acquired */
  onLockAcquired?: () => void;
  /** Callback when lock is released */
  onLockReleased?: () => void;
  /** Callback when lock acquisition fails */
  onLockFailed?: (error: string) => void;
  /** Callback when form becomes read-only */
  onReadOnlyMode?: () => void;
  /** Callback when inactivity warning should be shown */
  onInactivityWarning?: (remainingTime: number) => void;
}

/**
 * Form lock hook return value
 */
export interface UseFormLockReturn {
  /** Current lock state */
  lockState: FormLockState;
  /** Current form mode */
  formMode: FormLockMode;
  /** Whether form is editable */
  isEditable: boolean;
  /** Whether form is read-only */
  isReadOnly: boolean;
  /** Whether lock operation is in progress */
  isLoading: boolean;
  /** Error message if any */
  error?: string;
  /** User who currently holds the lock */
  lockedByUser?: { id: string; name: string; email: string };
  /** Time remaining before lock expires (in milliseconds) */
  remainingTime: number;
  /** Whether inactivity warning should be shown */
  showInactivityWarning: boolean;

  // Actions (only internal/automatic actions, no manual lock operations)
  resetInactivityTimer: () => void;
}

/**
 * Form lock status component props
 */
export interface FormLockStatusProps {
  /** Whether to show detailed lock information */
  showDetails?: boolean;
  /** Custom CSS classes */
  className?: string;
  /** Callback when user wants to view in read-only mode */
  onViewReadOnly?: () => void;
  /** Whether to show action buttons */
  showActions?: boolean;
}

/**
 * Form lock notification props
 */
export interface FormLockNotificationProps {
  /** Type of notification */
  type: "success" | "warning" | "error" | "info";
  /** Notification message */
  message: string;
  /** Additional details */
  details?: string;
  /** Whether notification is visible */
  visible: boolean;
  /** Callback when notification is dismissed */
  onDismiss?: () => void;
  /** Auto-dismiss timeout in milliseconds */
  autoHideDelay?: number;
}

/**
 * Default timer configuration
 */
export const DEFAULT_FORM_LOCK_TIMER_CONFIG: FormLockTimerConfig = {
  refreshInterval: 5 * 60 * 1000, // 5 minutes
  inactivityTimeout: 10 * 60 * 1000, // 10 minutes
  warningTime: 2 * 60 * 1000, // 2 minutes
  retryInterval: 30 * 1000, // 30 seconds
};

/**
 * Form lock error types
 */
export enum FormLockErrorType {
  NETWORK_ERROR = "network_error",
  ALREADY_LOCKED = "already_locked",
  LOCK_EXPIRED = "lock_expired",
  UNAUTHORIZED = "unauthorized",
  INVALID_REQUEST = "invalid_request",
  SERVER_ERROR = "server_error",
  UNKNOWN_ERROR = "unknown_error",
}

/**
 * Form lock error details
 */
export interface FormLockError {
  type: FormLockErrorType;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
}
